import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  Stack,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Modal,
  CircularProgress,
  Alert,
  Divider,
  Grid,
} from "@mui/material";
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import LocalFalconService from "../../services/localFalcon/localFalcon.service";
import LocalFalconDashboard from "../../components/localFalcon/LocalFalconDashboard.component";

interface ScanHistoryRecord {
  id: number;
  user_id: number;
  business_name: string;
  keyword: string;
  place_id: string;
  latitude: number;
  longitude: number;
  grid_size: number;
  radius_km: number;
  location_name: string;
  location_city: string;
  location_state: string;
  total_points: number;
  found_points: number;
  visibility_percent: number;
  average_ranking_position: number;
  share_of_local_voice: number;
  scan_status: "pending" | "completed" | "failed";
  created_at: string;
  user_name?: string;
  user_email?: string;
  request_data?: any;
  response_data?: any;
}

interface LocalFalconHistoryScreenProps {
  title?: string;
}

const LocalFalconHistoryScreen: React.FC<LocalFalconHistoryScreenProps> = ({
  title,
}) => {
  const dispatch = useDispatch();
  const [localFalconService] = useState(new LocalFalconService(dispatch));
  const [loading, setLoading] = useState(false);
  const [scanHistory, setScanHistory] = useState<ScanHistoryRecord[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // Filters
  const [searchKeyword, setSearchKeyword] = useState("");
  const [searchBusiness, setSearchBusiness] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [sortBy, setSortBy] = useState("created_at");
  const [sortOrder, setSortOrder] = useState<"ASC" | "DESC">("DESC");

  // Modal state
  const [selectedScan, setSelectedScan] = useState<ScanHistoryRecord | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [scanResult, setScanResult] = useState<any>(null);

  const user = useSelector((state: any) => state.authReducer?.userInfo);
  const isAdmin = user?.role === "admin" || user?.isAdmin;

  useEffect(() => {
    loadScanHistory();
  }, [
    pagination.page,
    pagination.limit,
    searchKeyword,
    searchBusiness,
    statusFilter,
    sortBy,
    sortOrder,
  ]);

  const loadScanHistory = async () => {
    try {
      setLoading(true);

      const params = {
        page: pagination.page,
        limit: pagination.limit,
        sortBy,
        sortOrder,
        ...(searchKeyword && { keyword: searchKeyword }),
        ...(searchBusiness && { businessName: searchBusiness }),
        ...(statusFilter && { status: statusFilter }),
      };

      const response = isAdmin
        ? await localFalconService.getAllUserScanHistory(params)
        : await localFalconService.getUserScanHistory(params);

      if (response.success) {
        setScanHistory(response.data);
        setPagination((prev) => ({
          ...prev,
          total: response.pagination.total,
          totalPages: response.pagination.totalPages,
        }));
      }
    } catch (error: any) {
      console.error("Failed to load scan history:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPagination((prev) => ({ ...prev, page: value }));
  };

  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    loadScanHistory();
  };

  const handleClearFilters = () => {
    setSearchKeyword("");
    setSearchBusiness("");
    setStatusFilter("");
    setSortBy("created_at");
    setSortOrder("DESC");
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleLoadResults = async (record: ScanHistoryRecord) => {
    try {
      setModalLoading(true);
      setSelectedScan(record);
      setIsModalOpen(true);

      // Get full scan data with response
      const response = await localFalconService.getScanHistoryRecord(record.id);

      if (response.success && response.data.response_data) {
        // Transform the stored response data to match expected format
        const transformedResult = {
          keyword: record.keyword,
          businessName: record.business_name,
          gridConfiguration: {
            lat: parseFloat(record.latitude?.toString() || "0") || 0,
            lng: parseFloat(record.longitude?.toString() || "0") || 0,
            gridSize: record.grid_size,
            radius: parseFloat(record.radius_km?.toString() || "0") || 0,
            unit: "km",
          },
          gridPoints: response.data.response_data.grid_points || [],
          rankings: response.data.response_data.rankings || [],
          averagePosition: record.average_ranking_position,
          visibilityPercentage: record.visibility_percent,
          totalSearches: record.total_points,
          foundInResults: record.found_points,
          rawResponse: response.data.response_data,
        };

        setScanResult(transformedResult);
      }
    } catch (error: any) {
      console.error("Failed to load scan results:", error);
    } finally {
      setModalLoading(false);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedScan(null);
    setScanResult(null);
  };

  const handleDeleteRecord = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this scan record?")) {
      try {
        const response = await localFalconService.deleteScanHistoryRecord(id);
        if (response.success) {
          loadScanHistory();
        }
      } catch (error: any) {
        console.error("Failed to delete scan record:", error);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "success";
      case "failed":
        return "error";
      case "pending":
        return "warning";
      default:
        return "default";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatLocation = (record: ScanHistoryRecord) => {
    // First try to show city, state if available
    if (record.location_city && record.location_state) {
      return `${record.location_city}, ${record.location_state}`;
    }

    // Otherwise try to show coordinates if they're valid numbers
    const lat = parseFloat(record.latitude?.toString() || "0");
    const lng = parseFloat(record.longitude?.toString() || "0");

    if (!isNaN(lat) && !isNaN(lng) && lat !== 0 && lng !== 0) {
      return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
    }

    // Fallback to N/A if no valid location data
    return "N/A";
  };

  const formatPercentage = (value: any, decimals: number = 1) => {
    const num = parseFloat(value?.toString() || "0");
    return !isNaN(num) ? num.toFixed(decimals) : "0.0";
  };

  const formatNumber = (value: any, decimals: number = 1) => {
    const num = parseFloat(value?.toString() || "0");
    return !isNaN(num) ? num.toFixed(decimals) : "0.0";
  };

  const formatInteger = (value: any) => {
    const num = parseInt(value?.toString() || "0");
    return !isNaN(num) ? num.toString() : "0";
  };

  return (
    <LeftMenuComponent>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          {title || "Local Falcon Scan History"}
        </Typography>
        <Divider sx={{ mt: 2, mb: 2 }} />
        {/* Filters */}
        <Grid container spacing={3} sx={{ mb: 3, alignItems: "flex-end" }}>
          <Grid item xs={12} md={6} lg={3}>
            <TextField
              variant="filled"
              placeholder="Search by keyword..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              sx={{
                "& .MuiFilledInput-root": {
                  backgroundColor: "var(--whiteColor) !important",
                  borderRadius: "5px !important",
                  paddingTop: "8px !important",
                  paddingBottom: "8px !important",
                  display: "flex",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                },
                "& .MuiFilledInput-input": {
                  padding: "12px 12px 8px 12px !important",
                  textAlign: "left",
                },
              }}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <TextField
              variant="filled"
              placeholder="Search by business..."
              value={searchBusiness}
              onChange={(e) => setSearchBusiness(e.target.value)}
              sx={{
                "& .MuiFilledInput-root": {
                  backgroundColor: "var(--whiteColor) !important",
                  borderRadius: "5px !important",
                  paddingTop: "8px !important",
                  paddingBottom: "8px !important",
                  display: "flex",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                },
                "& .MuiFilledInput-input": {
                  padding: "12px 12px 8px 12px !important",
                  textAlign: "left",
                },
              }}
              fullWidth
            />
          </Grid>

          {/* <Grid item xs={12} md={6} lg={2}>
            <FormControl className="commonSelect" variant="filled" fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
              </Select>
            </FormControl>
          </Grid> */}

          <Grid item xs={12} md={6} lg={2}>
            <FormControl
              className="commonSelect"
              variant="filled"
              fullWidth
              sx={{
                "& .MuiFilledInput-root": {
                  backgroundColor: "var(--whiteColor) !important",
                  borderRadius: "5px !important",
                  paddingTop: "8px !important",
                  paddingBottom: "8px !important",
                  display: "flex",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                },
                "& .MuiSelect-select": {
                  padding: "12px 12px 8px 12px !important",
                  display: "flex",
                  alignItems: "center",
                },
              }}
            >
              <InputLabel>Sort By</InputLabel>
              <Select
                value={sortBy}
                label="Sort By"
                onChange={(e) => setSortBy(e.target.value)}
              >
                <MenuItem value="created_at">Date</MenuItem>
                <MenuItem value="business_name">Business</MenuItem>
                <MenuItem value="keyword">Keyword</MenuItem>
                <MenuItem value="visibility_percent">Visibility</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6} lg={2}>
            <FormControl
              className="commonSelect"
              variant="filled"
              fullWidth
              sx={{
                "& .MuiFilledInput-root": {
                  backgroundColor: "var(--whiteColor) !important",
                  borderRadius: "5px !important",
                  paddingTop: "8px !important",
                  paddingBottom: "8px !important",
                  display: "flex",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "var(--whiteColor) !important",
                  },
                },
                "& .MuiSelect-select": {
                  padding: "12px 12px 8px 12px !important",
                  display: "flex",
                  alignItems: "center",
                },
              }}
            >
              <InputLabel>Order</InputLabel>
              <Select
                value={sortOrder}
                label="Order"
                onChange={(e) => setSortOrder(e.target.value as "ASC" | "DESC")}
              >
                <MenuItem value="DESC">Desc</MenuItem>
                <MenuItem value="ASC">Asc</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6} lg={1}>
            <Button
              variant="contained"
              className="commonShapeBtn"
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              fullWidth
            >
              Search
            </Button>
          </Grid>

          <Grid item xs={12} md={6} lg={1}>
            <Button
              variant="outlined"
              onClick={handleClearFilters}
              className="commonShapeBtn"
              startIcon={<FilterIcon />}
              fullWidth
            >
              Clear
            </Button>
          </Grid>
        </Grid>

        {/* Results Table */}
        <TableContainer className="commonTable">
          <Table aria-label="scan history table">
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Business Name</TableCell>
                <TableCell>Keyword</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Grid Size</TableCell>
                <TableCell>Visibility</TableCell>
                <TableCell>Avg Rank</TableCell>
                <TableCell>Found/Total</TableCell>
                <TableCell>Status</TableCell>
                {isAdmin && <TableCell>User</TableCell>}
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={isAdmin ? 11 : 10} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : scanHistory.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isAdmin ? 11 : 10} align="center">
                    No scan history found
                  </TableCell>
                </TableRow>
              ) : (
                scanHistory.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell data-label="Date">
                      {formatDate(record.created_at)}
                    </TableCell>
                    <TableCell data-label="Business Name">
                      {record.business_name}
                    </TableCell>
                    <TableCell data-label="Keyword">{record.keyword}</TableCell>
                    <TableCell data-label="Location">
                      {formatLocation(record)}
                    </TableCell>
                    <TableCell data-label="Grid Size">
                      {formatInteger(record.grid_size)}x
                      {formatInteger(record.grid_size)}
                    </TableCell>
                    <TableCell data-label="Visibility">
                      {formatPercentage(record.visibility_percent)}%
                    </TableCell>
                    <TableCell data-label="Avg Rank">
                      {formatNumber(record.average_ranking_position)}
                    </TableCell>
                    <TableCell data-label="Found/Total">
                      {formatInteger(record.found_points)}/
                      {formatInteger(record.total_points)}
                    </TableCell>
                    <TableCell data-label="Status">
                      <Chip
                        label={record.scan_status}
                        color={getStatusColor(record.scan_status) as any}
                        size="small"
                      />
                    </TableCell>
                    {isAdmin && (
                      <TableCell data-label="User">
                        {record.user_name || record.user_email}
                      </TableCell>
                    )}
                    <TableCell data-label="Actions">
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="Load Results">
                          <IconButton
                            className="emptyBtn"
                            onClick={() => handleLoadResults(record)}
                            disabled={record.scan_status !== "completed"}
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                        {/* <Tooltip title="Delete">
                          <IconButton
                            className="emptyBtn"
                            onClick={() => handleDeleteRecord(record.id)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip> */}
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
            <Pagination
              count={pagination.totalPages}
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        )}

        {/* Results Modal */}
        <Modal
          open={isModalOpen}
          onClose={handleCloseModal}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: 2,
          }}
        >
          <Box
            sx={{
              width: "95vw",
              height: "95vh",
              bgcolor: "background.paper",
              borderRadius: 2,
              boxShadow: 24,
              position: "relative",
              overflow: "hidden",
              display: "flex",
              flexDirection: "column",
              padding: 2,
            }}
          >
            {/* Modal Header */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
                p: 1,
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 1000,
              }}
            >
              <IconButton
                onClick={handleCloseModal}
                sx={{
                  color: "text.primary",
                  bgcolor: "background.paper",
                  boxShadow: 2,
                  border: "1px solid",
                  borderColor: "divider",
                  "&:hover": {
                    bgcolor: "grey.100",
                    boxShadow: 3,
                  },
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>

            {/* Modal Content */}
            <Box
              sx={{
                height: "100%",
                overflow: "auto",
                p: 0,
                "&::-webkit-scrollbar": {
                  width: "8px",
                },
                "&::-webkit-scrollbar-track": {
                  background: "#f1f1f1",
                  borderRadius: "4px",
                },
                "&::-webkit-scrollbar-thumb": {
                  background: "#c1c1c1",
                  borderRadius: "4px",
                  "&:hover": {
                    background: "#a8a8a8",
                  },
                },
              }}
            >
              {modalLoading ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  <CircularProgress />
                </Box>
              ) : scanResult ? (
                <LocalFalconDashboard
                  scanResult={scanResult}
                  trendData={[]}
                  alerts={[]}
                  competitors={[]}
                  loading={false}
                />
              ) : (
                <Box sx={{ p: 3 }}>
                  <Alert severity="error">Failed to load scan results</Alert>
                </Box>
              )}
            </Box>
          </Box>
        </Modal>
      </Box>
    </LeftMenuComponent>
  );
};

export default LocalFalconHistoryScreen;
