require('dotenv').config();
const Business = require('./models/business.models');

async function testBusinessFetchAll() {
  try {
    console.log('Testing Business.fetchAll method...');
    
    // Test with a user ID that should have admin role
    // You may need to adjust this based on your actual data
    const userId = 1; // Assuming user ID 1 exists and has admin role
    
    console.log(`Calling Business.fetchAll with userId: ${userId}`);
    const results = await Business.fetchAll(userId);
    
    console.log('Results received:');
    console.log('Number of results:', results.length);
    console.log('First few results:', results.slice(0, 3));
    
    // Test the raw query directly
    console.log('\n--- Testing raw query ---');
    const pool = require('./config/db');
    const rawResults = await pool.query(
      "SELECT DISTINCT bm.* FROM gmb_businesses_master bm LEFT JOIN user_business ub ON ub.businessId = bm.id LEFT JOIN users u ON ub.userId = u.id;"
    );
    
    console.log('Raw query results count:', rawResults.length);
    console.log('Raw query first few results:', rawResults.slice(0, 3));
    
    process.exit(0);
  } catch (error) {
    console.error('Error testing Business.fetchAll:', error);
    process.exit(1);
  }
}

testBusinessFetchAll();
