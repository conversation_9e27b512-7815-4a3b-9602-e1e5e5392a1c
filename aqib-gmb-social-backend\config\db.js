const mysql = require("mysql2/promise");

// Create the connection pool using mysql2/promise for better consistency
const pool = mysql.createPool({
  host: process.env.APP_DB_HOST,
  user: process.env.APP_DB_USER,
  password: process.env.APP_DB_PASSWORD,
  database: process.env.APP_DB_NAME,
  connectionLimit: 10, // Maximum number of connections in the pool
  queueLimit: 0,
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000,
  // Ensure consistent behavior
  multipleStatements: false,
  // Set transaction isolation level to ensure consistent reads
  typeCast: function (field, next) {
    if (field.type === "TINY" && field.length === 1) {
      return field.string() === "1"; // 1 = true, 0 = false
    }
    return next();
  },
});

// Create a wrapper that returns only the rows (first element of the array)
// to maintain compatibility with existing code
const poolWrapper = {
  async query(sql, params = []) {
    try {
      const [rows] = await pool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  },

  // For backward compatibility, also expose the original pool methods
  getConnection: () => pool.getConnection(),
  end: () => pool.end(),
};

module.exports = poolWrapper;
