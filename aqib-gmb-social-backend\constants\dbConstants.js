const RoleType = {
  Admin: 1,
  Manager: 2,
  User: 3,
};

// Google My Business Location Status Mapping
const GMB_LOCATION_STATUS = {
  // Google Status -> Application StatusId mapping
  OPEN: 1, // Active/Open location
  CLOSED_TEMPORARILY: 2, // Temporarily closed
  CLOSED_PERMANENTLY: 3, // Permanently closed
  OPEN_FOR_BUSINESS_UNSPECIFIED: 1, // Default to active if unspecified
};

// Application Status Constants
const LOCATION_STATUS = {
  ACTIVE: 1,
  TEMPORARILY_CLOSED: 2,
  PERMANENTLY_CLOSED: 3,
};

// Helper function to map Google status to application statusId
const mapGoogleStatusToStatusId = (googleStatus) => {
  if (!googleStatus) {
    return LOCATION_STATUS.ACTIVE; // Default to active if no status provided
  }

  return GMB_LOCATION_STATUS[googleStatus] || LOCATION_STATUS.ACTIVE;
};

module.exports = {
  RoleType,
  GMB_LOCATION_STATUS,
  LOCATION_STATUS,
  mapGoogleStatusToStatusId,
};
